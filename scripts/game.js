class Game {
    constructor() {
        this.canvas = new GameCanvas('gameCanvas');
        this.player = new Player('Demo User');
        this.lastTime = 0;
        this.currentZoneElement = document.getElementById('current-zone');
        
        this.setupInputHandlers();
        this.gameLoop();
        
        // Initial zone update
        this.updateCurrentZone();
        
        console.log('Team Building Game Demo Started!');
        console.log('Use WASD or Arrow Keys to move around');
    }
    
    setupInputHandlers() {
        // Keyboard event listeners
        document.addEventListener('keydown', (event) => {
            this.player.onKeyDown(event.key);
            event.preventDefault(); // Prevent page scrolling
        });
        
        document.addEventListener('keyup', (event) => {
            this.player.onKeyUp(event.key);
            event.preventDefault();
        });
        
        // Canvas click to move (optional feature)
        this.canvas.canvas.addEventListener('click', (event) => {
            const rect = this.canvas.canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;
            
            // Smooth movement towards clicked point (simple implementation)
            this.movePlayerTowards(x, y);
        });
        
        // Prevent context menu on right click
        this.canvas.canvas.addEventListener('contextmenu', (event) => {
            event.preventDefault();
        });
        
        // Focus canvas for keyboard input
        this.canvas.canvas.setAttribute('tabindex', '0');
        this.canvas.canvas.focus();
    }
    
    gameLoop(currentTime = 0) {
        const deltaTime = (currentTime - this.lastTime) / 1000;
        this.lastTime = currentTime;
        
        // Update game state
        this.update(deltaTime);
        
        // Render everything
        this.render();
        
        // Continue loop
        requestAnimationFrame((time) => this.gameLoop(time));
    }
    
    update(deltaTime) {
        // Update player
        const playerMoved = this.player.update(deltaTime, this.canvas.width, this.canvas.height);
        
        // Update zone information if player moved
        if (playerMoved) {
            this.updateCurrentZone();
        }
    }
    
    render() {
        // Clear and draw background
        this.canvas.drawBackground();
        
        // Draw player
        this.player.draw(this.canvas.ctx);
        
        // Draw additional UI elements on canvas if needed
        this.drawCanvasUI();
    }
    
    drawCanvasUI() {
        const ctx = this.canvas.ctx;
        
        // Draw instructions in corner
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.fillRect(10, 10, 200, 60);
        
        ctx.fillStyle = '#FFFFFF';
        ctx.font = '12px Arial';
        ctx.textAlign = 'left';
        ctx.fillText('WASD or Arrow Keys to move', 20, 30);
        ctx.fillText('Click anywhere to move there', 20, 45);
        ctx.fillText('Explore the personality zones!', 20, 60);
    }
    
    updateCurrentZone() {
        const position = this.player.getPosition();
        const zone = this.canvas.getZone(position.x, position.y);
        
        if (zone !== this.player.currentZone) {
            this.player.setCurrentZone(zone);
            this.currentZoneElement.textContent = zone;
            this.currentZoneElement.style.color = this.getZoneColor(zone);
            
            // Add a subtle animation effect
            this.currentZoneElement.style.transform = 'scale(1.1)';
            setTimeout(() => {
                this.currentZoneElement.style.transform = 'scale(1)';
            }, 200);
        }
    }
    
    getZoneColor(zone) {
        if (zone.includes('Blue')) return '#0066CC';
        if (zone.includes('Yellow')) return '#FFCC00';
        if (zone.includes('Green')) return '#00CC66';
        if (zone.includes('Red')) return '#CC0000';
        return '#667eea';
    }
    
    movePlayerTowards(targetX, targetY) {
        // Simple click-to-move implementation
        const currentPos = this.player.getPosition();
        const dx = targetX - currentPos.x;
        const dy = targetY - currentPos.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance > 5) { // Only move if click is far enough
            const moveX = (dx / distance) * 3; // Move 3 pixels per frame
            const moveY = (dy / distance) * 3;
            
            // Animate movement over several frames
            const animateMove = () => {
                const pos = this.player.getPosition();
                const newDx = targetX - pos.x;
                const newDy = targetY - pos.y;
                const newDistance = Math.sqrt(newDx * newDx + newDy * newDy);
                
                if (newDistance > 5) {
                    this.player.x += moveX;
                    this.player.y += moveY;
                    
                    // Keep within bounds
                    this.player.x = Math.max(this.player.radius, 
                        Math.min(this.canvas.width - this.player.radius, this.player.x));
                    this.player.y = Math.max(this.player.radius, 
                        Math.min(this.canvas.height - this.player.radius, this.player.y));
                    
                    requestAnimationFrame(animateMove);
                }
            };
            
            animateMove();
        }
    }
    
    // Method to get current game state (useful for demos)
    getGameState() {
        return {
            playerPosition: this.player.getPosition(),
            currentZone: this.player.currentZone,
            canvasSize: { width: this.canvas.width, height: this.canvas.height }
        };
    }
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
    const game = new Game();
    
    // Make game accessible globally for debugging
    window.game = game;
    
    // Add some demo instructions
    console.log('=== Team Building Game Demo ===');
    console.log('Available commands:');
    console.log('- game.getGameState() - Get current game state');
    console.log('- Use WASD or Arrow Keys to move');
    console.log('- Click on canvas to move to that location');
});
