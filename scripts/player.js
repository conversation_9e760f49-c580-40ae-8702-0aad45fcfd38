class Player {
    constructor(username = 'Demo User', x = 400, y = 300) {
        this.username = username;
        this.x = x;
        this.y = y;
        this.size = 30; // cube size
        this.speed = 200; // pixels per second
        this.baseColor = '#FFFFFF';
        this.strokeColor = '#333333';
        this.keys = {};
        this.currentZone = 'Center';

        // Cube color collection properties
        this.collectedColors = []; // Can hold up to 2 colors
        this.maxColors = 2;

        // Animation properties
        this.bobOffset = 0;
        this.bobSpeed = 3;
        this.isMoving = false;
        this.collectAnimation = 0;
        this.isCollecting = false;
    }

    update(deltaTime, canvasWidth, canvasHeight) {
        let moved = false;

        // Handle movement based on pressed keys
        if (this.keys['w'] || this.keys['W'] || this.keys['ArrowUp']) {
            this.y -= this.speed * deltaTime;
            moved = true;
        }
        if (this.keys['s'] || this.keys['S'] || this.keys['ArrowDown']) {
            this.y += this.speed * deltaTime;
            moved = true;
        }
        if (this.keys['a'] || this.keys['A'] || this.keys['ArrowLeft']) {
            this.x -= this.speed * deltaTime;
            moved = true;
        }
        if (this.keys['d'] || this.keys['D'] || this.keys['ArrowRight']) {
            this.x += this.speed * deltaTime;
            moved = true;
        }

        // Boundary checking (using half size instead of radius)
        const halfSize = this.size / 2;
        this.x = Math.max(halfSize, Math.min(canvasWidth - halfSize, this.x));
        this.y = Math.max(halfSize, Math.min(canvasHeight - halfSize, this.y));

        // Update animations
        this.isMoving = moved;
        if (this.isMoving) {
            this.bobOffset += this.bobSpeed * deltaTime;
        }

        // Update collection animation
        if (this.isCollecting) {
            this.collectAnimation += deltaTime * 8; // Fast animation
            if (this.collectAnimation >= Math.PI * 2) {
                this.isCollecting = false;
                this.collectAnimation = 0;
            }
        }

        return moved;
    }

    draw(ctx) {
        // Calculate bob animation
        const bobY = this.isMoving ? Math.sin(this.bobOffset) * 2 : 0;
        const drawY = this.y + bobY;

        // Collection animation scale
        const collectScale = this.isCollecting ? 1 + Math.sin(this.collectAnimation) * 0.2 : 1;
        const squareSize = this.size * collectScale;

        // Draw shadow
        ctx.beginPath();
        ctx.ellipse(this.x, this.y + this.size/2 + 8, squareSize * 0.6, squareSize * 0.2, 0, 0, Math.PI * 2);
        ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        ctx.fill();

        // Draw simple square
        this.drawSquare(ctx, this.x, drawY, squareSize);

        // Draw username with background
        this.drawUsername(ctx, this.x, drawY - squareSize/2 - 20);

        // Draw color collection indicator
        this.drawColorIndicator(ctx, this.x + squareSize/2 + 15, drawY);
    }

    drawSquare(ctx, x, y, size) {
        const halfSize = size / 2;

        // Define square position
        const square = {
            x: x - halfSize,
            y: y - halfSize,
            width: size,
            height: size
        };

        // Draw square with collected colors
        this.drawSquareWithColors(ctx, square);

        // Draw smiley face
        this.drawSmileyFace(ctx, x, y, size);
    }

    drawSquareWithColors(ctx, square) {
        // Draw square with collected colors
        if (this.collectedColors.length === 0) {
            // Default white square
            ctx.fillStyle = this.baseColor;
            ctx.fillRect(square.x, square.y, square.width, square.height);
        } else if (this.collectedColors.length === 1) {
            // One color on top half
            ctx.fillStyle = this.collectedColors[0];
            ctx.fillRect(square.x, square.y, square.width, square.height / 2);
            ctx.fillStyle = this.baseColor;
            ctx.fillRect(square.x, square.y + square.height / 2, square.width, square.height / 2);
        } else if (this.collectedColors.length === 2) {
            // Two colors split vertically
            ctx.fillStyle = this.collectedColors[0];
            ctx.fillRect(square.x, square.y, square.width, square.height / 2);
            ctx.fillStyle = this.collectedColors[1];
            ctx.fillRect(square.x, square.y + square.height / 2, square.width, square.height / 2);
        }

        // Draw square border
        ctx.strokeStyle = this.strokeColor;
        ctx.lineWidth = 3;
        ctx.strokeRect(square.x, square.y, square.width, square.height);

        // Draw dividing line if there are colors
        if (this.collectedColors.length > 0) {
            ctx.beginPath();
            ctx.moveTo(square.x, square.y + square.height / 2);
            ctx.lineTo(square.x + square.width, square.y + square.height / 2);
            ctx.stroke();
        }
    }

    drawSmileyFace(ctx, x, y, size) {
        // Draw eyes
        ctx.fillStyle = '#333333';
        const eyeSize = size * 0.08;
        const eyeOffset = size * 0.15;

        // Left eye
        ctx.beginPath();
        ctx.arc(x - eyeOffset, y - eyeOffset/2, eyeSize, 0, Math.PI * 2);
        ctx.fill();

        // Right eye
        ctx.beginPath();
        ctx.arc(x + eyeOffset, y - eyeOffset/2, eyeSize, 0, Math.PI * 2);
        ctx.fill();

        // Smile
        ctx.strokeStyle = '#333333';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.arc(x, y + eyeOffset/3, size * 0.2, 0, Math.PI);
        ctx.stroke();
    }

    drawUsername(ctx, x, y) {
        // Measure text for background
        ctx.font = 'bold 14px Arial';
        const textWidth = ctx.measureText(this.username).width;
        const padding = 6;

        // Draw background
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.fillRect(
            x - textWidth/2 - padding,
            y - 8,
            textWidth + padding * 2,
            16
        );

        // Draw text
        ctx.fillStyle = '#FFFFFF';
        ctx.textAlign = 'center';
        ctx.fillText(this.username, x, y + 4);
    }

    drawColorIndicator(ctx, x, y) {
        // Draw collected colors indicator
        ctx.font = 'bold 12px Arial';
        ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
        ctx.fillRect(x - 5, y - 30, 60, 60);

        ctx.fillStyle = '#FFFFFF';
        ctx.textAlign = 'left';
        ctx.fillText('Colors:', x, y - 15);
        ctx.fillText(`${this.collectedColors.length}/2`, x, y);

        // Draw small color squares
        for (let i = 0; i < this.collectedColors.length; i++) {
            ctx.fillStyle = this.collectedColors[i];
            ctx.fillRect(x + 5, y + 10 + (i * 15), 12, 12);
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 1;
            ctx.strokeRect(x + 5, y + 10 + (i * 15), 12, 12);
        }

        // Show instruction
        ctx.fillStyle = '#FFFFFF';
        ctx.font = '10px Arial';
        ctx.fillText('Press ENTER', x - 3, y + 45);
        ctx.fillText('to collect', x - 3, y + 55);
    }

    // Method to handle key press
    onKeyDown(key) {
        this.keys[key] = true;

        // Handle Enter key for color collection
        if (key === 'Enter') {
            this.collectColor();
        }
    }

    // Method to handle key release
    onKeyUp(key) {
        this.keys[key] = false;
    }

    // Color collection method
    collectColor() {
        const zoneColor = this.getZoneColor(this.currentZone);

        if (zoneColor) {
            if (this.collectedColors.length < this.maxColors) {
                // Add new color
                this.collectedColors.push(zoneColor);
                this.isCollecting = true;
                console.log(`Collected color: ${zoneColor} from ${this.currentZone}`);
            } else {
                // Clear all colors on third press
                this.collectedColors = [];
                this.isCollecting = true;
                console.log('Cleared all colors');
            }
        } else {
            // If in neutral zone or center, clear colors
            this.collectedColors = [];
            this.isCollecting = true;
            if (this.currentZone === 'Neutral Zone') {
                console.log('Cleared all colors (neutral zone)');
            } else {
                console.log('Cleared all colors (center area)');
            }
        }
    }

    // Get color based on zone
    getZoneColor(zone) {
        if (zone.includes('Blue')) return '#0066CC';
        if (zone.includes('Yellow')) return '#FFCC00';
        if (zone.includes('Green')) return '#00CC66';
        if (zone.includes('Red')) return '#CC0000';
        return null; // Neutral zone or center - no color
    }

    // Get current position
    getPosition() {
        return { x: this.x, y: this.y };
    }

    // Set zone information
    setCurrentZone(zone) {
        this.currentZone = zone;
    }

    // Get collected colors info
    getCollectedColors() {
        return {
            colors: [...this.collectedColors],
            count: this.collectedColors.length,
            maxColors: this.maxColors
        };
    }
}
