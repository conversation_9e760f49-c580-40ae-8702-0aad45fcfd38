class Player {
    constructor(username = 'Demo User', x = 400, y = 300) {
        this.username = username;
        this.x = x;
        this.y = y;
        this.radius = 15;
        this.speed = 200; // pixels per second
        this.color = '#FFFFFF';
        this.strokeColor = '#333333';
        this.keys = {};
        this.currentZone = 'Center';
        
        // Animation properties
        this.bobOffset = 0;
        this.bobSpeed = 3;
        this.isMoving = false;
    }
    
    update(deltaTime, canvasWidth, canvasHeight) {
        let moved = false;
        
        // Handle movement based on pressed keys
        if (this.keys['w'] || this.keys['W'] || this.keys['ArrowUp']) {
            this.y -= this.speed * deltaTime;
            moved = true;
        }
        if (this.keys['s'] || this.keys['S'] || this.keys['ArrowDown']) {
            this.y += this.speed * deltaTime;
            moved = true;
        }
        if (this.keys['a'] || this.keys['A'] || this.keys['ArrowLeft']) {
            this.x -= this.speed * deltaTime;
            moved = true;
        }
        if (this.keys['d'] || this.keys['D'] || this.keys['ArrowRight']) {
            this.x += this.speed * deltaTime;
            moved = true;
        }
        
        // Boundary checking
        this.x = Math.max(this.radius, Math.min(canvasWidth - this.radius, this.x));
        this.y = Math.max(this.radius, Math.min(canvasHeight - this.radius, this.y));
        
        // Update animation
        this.isMoving = moved;
        if (this.isMoving) {
            this.bobOffset += this.bobSpeed * deltaTime;
        }
        
        return moved;
    }
    
    draw(ctx) {
        // Calculate bob animation
        const bobY = this.isMoving ? Math.sin(this.bobOffset) * 2 : 0;
        const drawY = this.y + bobY;
        
        // Draw shadow
        ctx.beginPath();
        ctx.ellipse(this.x, this.y + this.radius + 5, this.radius * 0.8, this.radius * 0.3, 0, 0, Math.PI * 2);
        ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
        ctx.fill();
        
        // Draw player circle with gradient
        const gradient = ctx.createRadialGradient(
            this.x - 5, drawY - 5, 0,
            this.x, drawY, this.radius
        );
        gradient.addColorStop(0, '#FFFFFF');
        gradient.addColorStop(1, '#E0E0E0');
        
        ctx.beginPath();
        ctx.arc(this.x, drawY, this.radius, 0, Math.PI * 2);
        ctx.fillStyle = gradient;
        ctx.fill();
        ctx.strokeStyle = this.strokeColor;
        ctx.lineWidth = 3;
        ctx.stroke();
        
        // Draw simple face
        this.drawFace(ctx, this.x, drawY);
        
        // Draw username with background
        this.drawUsername(ctx, this.x, drawY - this.radius - 15);
    }
    
    drawFace(ctx, x, y) {
        // Eyes
        ctx.fillStyle = '#333333';
        ctx.beginPath();
        ctx.arc(x - 5, y - 3, 2, 0, Math.PI * 2);
        ctx.arc(x + 5, y - 3, 2, 0, Math.PI * 2);
        ctx.fill();
        
        // Smile
        ctx.strokeStyle = '#333333';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.arc(x, y + 2, 6, 0, Math.PI);
        ctx.stroke();
    }
    
    drawUsername(ctx, x, y) {
        // Measure text for background
        ctx.font = 'bold 14px Arial';
        const textWidth = ctx.measureText(this.username).width;
        const padding = 6;
        
        // Draw background
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.fillRect(
            x - textWidth/2 - padding,
            y - 8,
            textWidth + padding * 2,
            16
        );
        
        // Draw text
        ctx.fillStyle = '#FFFFFF';
        ctx.textAlign = 'center';
        ctx.fillText(this.username, x, y + 4);
    }
    
    // Method to handle key press
    onKeyDown(key) {
        this.keys[key] = true;
    }
    
    // Method to handle key release
    onKeyUp(key) {
        this.keys[key] = false;
    }
    
    // Get current position
    getPosition() {
        return { x: this.x, y: this.y };
    }
    
    // Set zone information
    setCurrentZone(zone) {
        this.currentZone = zone;
    }
}
