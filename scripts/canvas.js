class GameCanvas {
    constructor(canvasId, width = 800, height = 600) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        this.width = width;
        this.height = height;
        this.setupCanvas();
    }
    
    setupCanvas() {
        this.canvas.width = this.width;
        this.canvas.height = this.height;
        this.canvas.style.cursor = 'crosshair';
    }
    
    drawBackground() {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.width, this.height);
        
        // Draw four colored corners with smooth gradients
        this.drawCornerWithGradient(0, 0, this.width/2, this.height/2, '#0066CC', 'Blue'); // Top Left - Blue
        this.drawCornerWithGradient(this.width/2, 0, this.width/2, this.height/2, '#FFCC00', 'Yellow'); // Top Right - Yellow
        this.drawCornerWithGradient(0, this.height/2, this.width/2, this.height/2, '#00CC66', 'Green'); // Bottom Left - Green
        this.drawCornerWithGradient(this.width/2, this.height/2, this.width/2, this.height/2, '#CC0000', 'Red'); // Bottom Right - Red
        
        // Draw center lines for visual separation
        this.drawCenterLines();
        
        // Draw zone labels
        this.drawZoneLabels();
    }
    
    drawCornerWithGradient(x, y, width, height, color, label) {
        // Create gradient from center to corner
        const centerX = this.width / 2;
        const centerY = this.height / 2;
        const cornerX = x + (x === 0 ? 0 : width);
        const cornerY = y + (y === 0 ? 0 : height);
        
        const gradient = this.ctx.createRadialGradient(
            centerX, centerY, 0,
            cornerX, cornerY, Math.sqrt(width * width + height * height)
        );
        
        gradient.addColorStop(0, 'rgba(255, 255, 255, 0.8)');
        gradient.addColorStop(0.7, color + '80'); // Semi-transparent
        gradient.addColorStop(1, color);
        
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(x, y, width, height);
    }
    
    drawCenterLines() {
        this.ctx.strokeStyle = '#333333';
        this.ctx.lineWidth = 2;
        this.ctx.setLineDash([5, 5]);
        
        // Vertical line
        this.ctx.beginPath();
        this.ctx.moveTo(this.width / 2, 0);
        this.ctx.lineTo(this.width / 2, this.height);
        this.ctx.stroke();
        
        // Horizontal line
        this.ctx.beginPath();
        this.ctx.moveTo(0, this.height / 2);
        this.ctx.lineTo(this.width, this.height / 2);
        this.ctx.stroke();
        
        this.ctx.setLineDash([]); // Reset line dash
    }
    
    drawZoneLabels() {
        this.ctx.fillStyle = '#333333';
        this.ctx.font = 'bold 24px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.strokeStyle = 'white';
        this.ctx.lineWidth = 3;
        
        // Blue zone (top-left)
        this.ctx.strokeText('ANALYTICAL', this.width / 4, 50);
        this.ctx.fillText('ANALYTICAL', this.width / 4, 50);
        
        // Yellow zone (top-right)
        this.ctx.strokeText('EXPRESSIVE', (this.width * 3) / 4, 50);
        this.ctx.fillText('EXPRESSIVE', (this.width * 3) / 4, 50);
        
        // Green zone (bottom-left)
        this.ctx.strokeText('AMIABLE', this.width / 4, this.height - 30);
        this.ctx.fillText('AMIABLE', this.width / 4, this.height - 30);
        
        // Red zone (bottom-right)
        this.ctx.strokeText('DRIVER', (this.width * 3) / 4, this.height - 30);
        this.ctx.fillText('DRIVER', (this.width * 3) / 4, this.height - 30);
    }
    
    // Helper method to determine which zone a point is in
    getZone(x, y) {
        const centerX = this.width / 2;
        const centerY = this.height / 2;
        
        if (x < centerX && y < centerY) return 'Blue - Analytical';
        if (x >= centerX && y < centerY) return 'Yellow - Expressive';
        if (x < centerX && y >= centerY) return 'Green - Amiable';
        if (x >= centerX && y >= centerY) return 'Red - Driver';
        return 'Center';
    }
}
