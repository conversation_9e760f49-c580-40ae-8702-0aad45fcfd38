* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
    font-size: 1.2em;
    opacity: 0.9;
}

.game-area {
    display: flex;
    gap: 20px;
    justify-content: center;
    align-items: flex-start;
    margin-bottom: 30px;
}

.controls-info {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    min-width: 200px;
}

.controls-info h3 {
    margin-bottom: 15px;
    color: #333;
}

.controls-info p {
    margin-bottom: 10px;
    line-height: 1.4;
}

#current-zone {
    font-weight: bold;
    color: #667eea;
    font-size: 1.1em;
}

#gameCanvas {
    border: 3px solid white;
    border-radius: 10px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
    background: white;
}

.personality-legend {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    min-width: 250px;
}

.personality-legend h3 {
    margin-bottom: 15px;
    color: #333;
}

.legend-grid {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.color-box {
    width: 30px;
    height: 30px;
    border-radius: 5px;
    border: 2px solid #333;
}

.blue-bg { background-color: #0066CC; }
.yellow-bg { background-color: #FFCC00; }
.green-bg { background-color: #00CC66; }
.red-bg { background-color: #CC0000; }

.legend-text strong {
    display: block;
    margin-bottom: 5px;
    font-size: 1.1em;
}

.legend-text p {
    font-size: 0.9em;
    color: #666;
    line-height: 1.3;
}

.demo-info {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    text-align: center;
}

.demo-info h3 {
    margin-bottom: 15px;
    color: #333;
    font-size: 1.3em;
}

.demo-info p {
    line-height: 1.6;
    color: #555;
    font-size: 1.1em;
}

/* Responsive design */
@media (max-width: 1024px) {
    .game-area {
        flex-direction: column;
        align-items: center;
    }
    
    .controls-info,
    .personality-legend {
        max-width: 800px;
        width: 100%;
    }
    
    .legend-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }
}

@media (max-width: 768px) {
    #gameCanvas {
        width: 100%;
        max-width: 400px;
        height: 300px;
    }
    
    .legend-grid {
        grid-template-columns: 1fr;
    }
    
    header h1 {
        font-size: 2em;
    }
}
