/**
 * Google Apps Script Web App for Personality Arena Game
 * This serves the HTML file for the team building game
 */

function doGet() {
  return HtmlService.createTemplateFromFile('index')
    .evaluate()
    .setTitle('Personality Arena - Team Building Experience')
    .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL)
    .addMetaTag('viewport', 'width=device-width, initial-scale=1.0');
}

/**
 * Include function to load CSS and JS files
 * This allows us to separate our code into multiple files
 */
function include(filename) {
  return HtmlService.createHtmlOutputFromFile(filename).getContent();
}

/**
 * Optional: Log game events for analytics
 */
function logGameEvent(eventType, eventData) {
  console.log('Game Event:', eventType, eventData);
  // You could save to Google Sheets for analytics
  return 'Event logged';
}

/**
 * Optional: Save game data to Google Sheets
 */
function saveGameData(playerData) {
  try {
    // Uncomment and modify if you want to save data to Google Sheets
    /*
    const sheet = SpreadsheetApp.openById('YOUR_SHEET_ID').getActiveSheet();
    sheet.appendRow([
      new Date(),
      playerData.username || 'Anonymous',
      playerData.collectedColors || '[]',
      playerData.timeSpent || 0
    ]);
    */
    return 'Data saved successfully';
  } catch (error) {
    console.error('Error saving data:', error);
    return 'Error saving data';
  }
}
